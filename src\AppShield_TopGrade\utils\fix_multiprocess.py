#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : fix_multiprocess.py
# <AUTHOR> Assistant
# @Date    : 2025-08-27
# @Desc    : Fix multi-process VPN app issues by preserving appComponentFactory

import os
import logging
import subprocess
import execute_cmd

def check_and_preserve_app_component_factory(aapt_path, apk_path):
    """
    检查APK是否使用了appComponentFactory，如果有则返回其值
    Check if APK uses appComponentFactory and return its value if found
    """
    try:
        cmd = aapt_path + " dump xmltree " + apk_path + " AndroidManifest.xml"
        result = execute_cmd.execute_command(cmd, return_value=True)
        
        if "appComponentFactory" in result:
            lines = result.split('\n')
            for line in lines:
                if "appComponentFactory" in line and "=" in line:
                    # 解析类似这样的行: A: android:appComponentFactory(0x0101057a)="androidx.core.app.CoreComponentFactory"
                    start = line.find('"') + 1
                    end = line.find('"', start)
                    if start > 0 and end > start:
                        factory_value = line[start:end]
                        logging.debug("Found appComponentFactory: %s", factory_value)
                        return factory_value
        return None
    except Exception as e:
        logging.debug("Failed to check appComponentFactory: %s", str(e))
        return None

def restore_app_component_factory(manifest_editor_path, input_manifest, output_manifest, factory_value):
    """
    使用ManifestEditor恢复appComponentFactory属性
    Restore appComponentFactory attribute using ManifestEditor
    """
    try:
        cmd = 'java -jar ' + manifest_editor_path + ' ' + input_manifest + ' -aa android-appComponentFactory:' + factory_value + ' -o ' + output_manifest
        result = execute_cmd.execute_command(cmd, return_value=False)
        if result == '0':
            logging.debug("Successfully restored appComponentFactory: %s", factory_value)
            return True
        else:
            logging.debug("Failed to restore appComponentFactory")
            return False
    except Exception as e:
        logging.debug("Exception while restoring appComponentFactory: %s", str(e))
        return False

def fix_multiprocess_manifest(aapt_path, manifest_editor_path, original_apk_path, manifest_path):
    """
    修复多进程应用的AndroidManifest.xml
    Fix AndroidManifest.xml for multi-process applications
    """
    # 检查原始APK是否有appComponentFactory
    factory_value = check_and_preserve_app_component_factory(aapt_path, original_apk_path)
    
    if factory_value:
        logging.info("Detected multi-process app with appComponentFactory: %s", factory_value)
        
        # 创建临时文件来恢复属性
        temp_manifest = manifest_path + ".temp"
        
        if restore_app_component_factory(manifest_editor_path, manifest_path, temp_manifest, factory_value):
            # 替换原文件
            import shutil
            shutil.move(temp_manifest, manifest_path)
            logging.info("Successfully fixed multi-process manifest")
            return True
        else:
            # 清理临时文件
            if os.path.exists(temp_manifest):
                os.remove(temp_manifest)
            logging.error("Failed to fix multi-process manifest")
            return False
    else:
        logging.debug("No appComponentFactory found, no fix needed")
        return True
