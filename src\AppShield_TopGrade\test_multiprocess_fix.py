#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : test_multiprocess_fix.py
# <AUTHOR> Assistant
# @Date    : 2025-08-27
# @Desc    : Test script for multi-process VPN app fix

import os
import sys
import logging

# 添加utils目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'utils'))

import fix_multiprocess

def test_multiprocess_fix():
    """
    测试多进程修复功能
    Test multi-process fix functionality
    """
    logging.basicConfig(level=logging.DEBUG)
    
    # 设置工具路径
    root_dir = os.path.dirname(__file__)
    aapt_path = os.path.join(root_dir, "tools", "apktool", "aapt")
    manifest_editor_path = os.path.join(root_dir, "tools", "ManifestEditor.jar")
    
    # Windows平台添加.exe后缀
    if os.name == 'nt':
        aapt_path += '.exe'
    
    print("Testing multi-process fix...")
    print("AAPT path:", aapt_path)
    print("ManifestEditor path:", manifest_editor_path)
    
    # 检查工具是否存在
    if not os.path.exists(aapt_path):
        print("ERROR: AAPT not found at", aapt_path)
        return False
        
    if not os.path.exists(manifest_editor_path):
        print("ERROR: ManifestEditor not found at", manifest_editor_path)
        return False
    
    print("All tools found, fix is ready to use!")
    return True

if __name__ == "__main__":
    test_multiprocess_fix()
